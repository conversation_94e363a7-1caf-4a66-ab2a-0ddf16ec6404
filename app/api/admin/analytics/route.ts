import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requirePermission, logActivity } from '@/lib/user-management/middleware';
import { ACTIVITY_ACTIONS } from '@/lib/user-management/permissions';

// GET /api/admin/analytics - Get user analytics
export async function GET(request: NextRequest) {
  return requirePermission('analytics', 'read')(request, async (req, user) => {
    try {
      const { searchParams } = new URL(req.url);
      const period = searchParams.get('period') || '30d'; // 7d, 30d, 90d, 1y
      const metric = searchParams.get('metric') || 'all';

      // Calculate date range based on period
      const now = new Date();
      let startDate: Date;
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default: // 30d
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const analytics: any = {};

      // User metrics
      if (metric === 'all' || metric === 'users') {
        const [
          totalUsers,
          activeUsers,
          newUsers,
          usersByStatus,
          userGrowth,
        ] = await Promise.all([
          // Total users
          prisma.user.count(),
          
          // Active users (logged in within period)
          prisma.user.count({
            where: {
              lastActiveAt: {
                gte: startDate,
              },
            },
          }),
          
          // New users in period
          prisma.user.count({
            where: {
              createdAt: {
                gte: startDate,
              },
            },
          }),
          
          // Users by status
          prisma.user.groupBy({
            by: ['status'],
            _count: { status: true },
          }),
          
          // User growth over time
          prisma.$queryRawUnsafe(`
            SELECT 
              strftime('%Y-%m-%d', createdAt) as date,
              COUNT(*) as count
            FROM User 
            WHERE createdAt >= ?
            GROUP BY strftime('%Y-%m-%d', createdAt)
            ORDER BY date
          `, startDate) as { date: string; count: number }[],
        ]);

        analytics.users = {
          total: totalUsers,
          active: activeUsers,
          new: newUsers,
          byStatus: usersByStatus.map(item => ({
            status: item.status,
            count: item._count.status,
          })),
          growth: userGrowth,
        };
      }

      // Activity metrics
      if (metric === 'all' || metric === 'activity') {
        const [
          totalActivities,
          activitiesByAction,
          activitiesByResource,
          activityTimeline,
        ] = await Promise.all([
          // Total activities in period
          prisma.userLog.count({
            where: {
              timestamp: {
                gte: startDate,
              },
            },
          }),
          
          // Activities by action
          prisma.userLog.groupBy({
            by: ['action'],
            where: {
              timestamp: {
                gte: startDate,
              },
            },
            _count: { action: true },
            orderBy: { _count: { action: 'desc' } },
            take: 10,
          }),
          
          // Activities by resource
          prisma.userLog.groupBy({
            by: ['resource'],
            where: {
              timestamp: {
                gte: startDate,
              },
              resource: { not: null },
            },
            _count: { resource: true },
            orderBy: { _count: { resource: 'desc' } },
            take: 10,
          }),
          
          // Activity timeline
          prisma.$queryRawUnsafe(`
            SELECT 
              strftime('%Y-%m-%d', timestamp) as date,
              COUNT(*) as count
            FROM UserLog 
            WHERE timestamp >= ?
            GROUP BY strftime('%Y-%m-%d', timestamp)
            ORDER BY date
          `, startDate) as { date: string; count: number }[],
        ]);

        analytics.activity = {
          total: totalActivities,
          byAction: activitiesByAction.map(item => ({
            action: item.action,
            count: item._count.action,
          })),
          byResource: activitiesByResource.map(item => ({
            resource: item.resource,
            count: item._count.resource,
          })),
          timeline: activityTimeline,
        };
      }

      // Login metrics
      if (metric === 'all' || metric === 'logins') {
        const [
          totalLogins,
          successfulLogins,
          failedLogins,
          uniqueLoginUsers,
          loginTimeline,
        ] = await Promise.all([
          // Total login attempts
          prisma.loginHistory.count({
            where: {
              timestamp: {
                gte: startDate,
              },
            },
          }),
          
          // Successful logins
          prisma.loginHistory.count({
            where: {
              timestamp: {
                gte: startDate,
              },
              success: true,
            },
          }),
          
          // Failed logins
          prisma.loginHistory.count({
            where: {
              timestamp: {
                gte: startDate,
              },
              success: false,
            },
          }),
          
          // Unique users who logged in
          prisma.loginHistory.findMany({
            where: {
              timestamp: {
                gte: startDate,
              },
              success: true,
            },
            select: { userId: true },
            distinct: ['userId'],
          }).then(users => users.length),
          
          // Login timeline
          prisma.$queryRawUnsafe(`
            SELECT 
              strftime('%Y-%m-%d', timestamp) as date,
              COUNT(*) as total,
              SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
              SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed
            FROM LoginHistory 
            WHERE timestamp >= ?
            GROUP BY strftime('%Y-%m-%d', timestamp)
            ORDER BY date
          `, startDate) as { date: string; total: number; successful: number; failed: number }[],
        ]);

        analytics.logins = {
          total: totalLogins,
          successful: successfulLogins,
          failed: failedLogins,
          uniqueUsers: uniqueLoginUsers,
          successRate: totalLogins > 0 ? (successfulLogins / totalLogins * 100).toFixed(2) : 0,
          timeline: loginTimeline,
        };
      }

      // System metrics
      if (metric === 'all' || metric === 'system') {
        const [
          totalRoles,
          totalPermissions,
          rolesWithUsers,
          topRoles,
        ] = await Promise.all([
          // Total roles
          prisma.role.count(),
          
          // Total permissions
          prisma.permission.count(),
          
          // Roles with user counts
          prisma.role.findMany({
            include: {
              _count: {
                select: {
                  userRoles: true,
                },
              },
            },
          }),
          
          // Most assigned roles
          prisma.userRole.groupBy({
            by: ['roleId'],
            _count: { roleId: true },
            orderBy: { _count: { roleId: 'desc' } },
            take: 5,
          }),
        ]);

        const roleDetails = await prisma.role.findMany({
          where: {
            id: {
              in: topRoles.map(r => r.roleId),
            },
          },
        });

        analytics.system = {
          totalRoles,
          totalPermissions,
          roleDistribution: rolesWithUsers.map(role => ({
            id: role.id,
            name: role.name,
            displayName: role.displayName,
            userCount: role._count.userRoles,
          })),
          topRoles: topRoles.map(item => {
            const role = roleDetails.find(r => r.id === item.roleId);
            return {
              roleId: item.roleId,
              roleName: role?.displayName || role?.name || 'Unknown',
              userCount: item._count.roleId,
            };
          }),
        };
      }

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'analytics',
        undefined,
        { period, metric },
        req
      );

      return NextResponse.json({
        analytics,
        period,
        startDate,
        endDate: now,
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
      return NextResponse.json(
        { error: 'Failed to fetch analytics' },
        { status: 500 }
      );
    }
  });
}
