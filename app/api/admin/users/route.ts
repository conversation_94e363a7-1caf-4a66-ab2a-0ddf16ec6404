import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requirePermission, logActivity, validateRequestData } from '@/lib/user-management/middleware';
import { ACTIVITY_ACTIONS } from '@/lib/user-management/permissions';
import bcrypt from 'bcryptjs';

// GET /api/admin/users - List users with pagination and filters
export async function GET(request: NextRequest) {
  return requirePermission('users', 'read')(request, async (req, user) => {
    try {
      const { searchParams } = new URL(req.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '10');
      const search = searchParams.get('search') || '';
      const status = searchParams.get('status') || '';
      const role = searchParams.get('role') || '';
      const sortBy = searchParams.get('sortBy') || 'createdAt';
      const sortOrder = searchParams.get('sortOrder') || 'desc';

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search } },
          { email: { contains: search } },
          { username: { contains: search } },
        ];
      }

      if (status) {
        where.status = status;
      }

      if (role) {
        where.userRoles = {
          some: {
            role: {
              name: role,
            },
          },
        };
      }

      // Get users with pagination
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            userRoles: {
              include: {
                role: true,
              },
            },
            _count: {
              select: {
                workflows: true,
                userLogs: true,
              },
            },
          },
        }),
        prisma.user.count({ where }),
      ]);

      // Format response
      const formattedUsers = users.map(user => ({
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        avatar: user.avatar,
        status: user.status,
        isAdmin: user.isAdmin,
        isSuperAdmin: user.isSuperAdmin,
        emailVerified: user.emailVerified,
        lastLoginAt: user.lastLoginAt,
        lastActiveAt: user.lastActiveAt,
        loginCount: user.loginCount,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        roles: user.userRoles.map(ur => ur.role),
        stats: {
          workflowCount: user._count.workflows,
          activityCount: user._count.userLogs,
        },
      }));

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.API_CALLED,
        'users',
        undefined,
        { action: 'list', filters: { search, status, role } },
        req
      );

      return NextResponse.json({
        users: formattedUsers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }
  });
}

// POST /api/admin/users - Create new user
export async function POST(request: NextRequest) {
  return requirePermission('users', 'create')(request, async (req, user) => {
    try {
      const body = await req.json();

      const validation = validateRequestData(
        body,
        ['email', 'name', 'password'],
        ['username', 'bio', 'avatar', 'status', 'isAdmin', 'roles']
      );

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { email, name, password, username, bio, avatar, status, isAdmin, roles } = validation.validatedData!;

      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: email as string },
            ...(username ? [{ username: username as string }] : []),
          ],
        },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: 'User with this email or username already exists' },
          { status: 400 }
        );
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password as string, 12);

      // Generate username if not provided
      const finalUsername = username || `user_${Date.now()}`;

      // Create user
      const newUser = await prisma.user.create({
        data: {
          email: email as string,
          name: name as string,
          password: hashedPassword,
          username: finalUsername,
          bio: bio as string,
          avatar: avatar as string,
          status: (status as any) || 'ACTIVE',
          isAdmin: isAdmin as boolean || false,
          emailVerified: true, // Auto-verify admin-created users
          createdById: user.id,
        },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      // Assign roles if provided
      if (roles && Array.isArray(roles)) {
        for (const roleId of roles) {
          await prisma.userRole.create({
            data: {
              userId: newUser.id,
              roleId: roleId as string,
              assignedBy: user.id,
            },
          });
        }
      }

      await logActivity(
        user.id,
        ACTIVITY_ACTIONS.USER_CREATED,
        'users',
        newUser.id,
        {
          email: newUser.email,
          name: newUser.name,
          roles: roles || [],
        },
        req
      );

      return NextResponse.json({
        message: 'User created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          username: newUser.username,
          name: newUser.name,
          status: newUser.status,
          isAdmin: newUser.isAdmin,
          createdAt: newUser.createdAt,
        },
      });
    } catch (error) {
      console.error('Error creating user:', error);
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }
  });
}
