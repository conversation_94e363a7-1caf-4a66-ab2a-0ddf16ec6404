import CredentialsProvider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"
import { NextAuthOptions } from "next-auth"
import { logLoginAttempt, logActivity } from "@/lib/user-management/middleware"
import { ACTIVITY_ACTIONS } from "@/lib/user-management/permissions"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "<PERSON><PERSON>", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Find user by email
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email
            }
          })

          if (!user) {
            return null
          }

          // Check if email is verified
          if (!user.emailVerified) {
            await logLoginAttempt(user.id, false, "Email not verified")
            throw new Error("Please verify your email address before logging in")
          }

          // Check if user is active
          if (user.status !== 'ACTIVE') {
            await logLoginAttempt(user.id, false, `Account status: ${user.status}`)
            throw new Error("Account is not active")
          }

          // Verify password
          if (!user.password) {
            await logLoginAttempt(user.id, false, "No password set")
            return null
          }
          const isPasswordValid = await bcrypt.compare(credentials.password, user.password)

          if (!isPasswordValid) {
            await logLoginAttempt(user.id, false, "Invalid password")
            return null
          }

          // Log successful login
          await logLoginAttempt(user.id, true)
          await logActivity(user.id, ACTIVITY_ACTIONS.LOGIN)

          // Return user object
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.avatar,
          }
        } catch (error) {
          console.error("Auth error:", error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async jwt({ token, user }) {
      // Persist the user ID to the token right after signin
      if (user) {
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token && session.user) {
        (session.user as any).id = token.id as string
      }
      return session
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
}
