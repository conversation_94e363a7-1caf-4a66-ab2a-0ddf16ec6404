// User Management System - Permission Utilities

export interface Permission {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  resource: string;
  action: string;
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  permissions: Permission[];
}

export interface UserWithRoles {
  id: string;
  email: string;
  name?: string;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  userRoles: {
    role: Role;
  }[];
}

// Default system permissions
export const SYSTEM_PERMISSIONS = [
  // User Management
  { resource: 'users', action: 'create', displayName: 'Create Users', description: 'Create new user accounts' },
  { resource: 'users', action: 'read', displayName: 'View Users', description: 'View user information' },
  { resource: 'users', action: 'update', displayName: 'Edit Users', description: 'Edit user information' },
  { resource: 'users', action: 'delete', displayName: 'Delete Users', description: 'Delete user accounts' },
  { resource: 'users', action: 'manage', displayName: 'Manage Users', description: 'Full user management access' },

  // Role Management
  { resource: 'roles', action: 'create', displayName: 'Create Roles', description: 'Create new roles' },
  { resource: 'roles', action: 'read', displayName: 'View Roles', description: 'View role information' },
  { resource: 'roles', action: 'update', displayName: 'Edit Roles', description: 'Edit role information' },
  { resource: 'roles', action: 'delete', displayName: 'Delete Roles', description: 'Delete roles' },
  { resource: 'roles', action: 'assign', displayName: 'Assign Roles', description: 'Assign roles to users' },

  // Workflow Management
  { resource: 'workflows', action: 'create', displayName: 'Create Workflows', description: 'Create new workflows' },
  { resource: 'workflows', action: 'read', displayName: 'View Workflows', description: 'View workflow information' },
  { resource: 'workflows', action: 'update', displayName: 'Edit Workflows', description: 'Edit workflow information' },
  { resource: 'workflows', action: 'delete', displayName: 'Delete Workflows', description: 'Delete workflows' },
  { resource: 'workflows', action: 'execute', displayName: 'Execute Workflows', description: 'Execute workflows' },

  // Marketplace Management
  { resource: 'marketplace', action: 'read', displayName: 'View Marketplace', description: 'Access marketplace' },
  { resource: 'marketplace', action: 'purchase', displayName: 'Purchase Nodes', description: 'Purchase marketplace nodes' },
  { resource: 'marketplace', action: 'publish', displayName: 'Publish Nodes', description: 'Publish nodes to marketplace' },
  { resource: 'marketplace', action: 'manage', displayName: 'Manage Marketplace', description: 'Full marketplace management' },

  // Analytics & Logs
  { resource: 'analytics', action: 'read', displayName: 'View Analytics', description: 'View system analytics' },
  { resource: 'logs', action: 'read', displayName: 'View Logs', description: 'View system logs' },
  { resource: 'logs', action: 'manage', displayName: 'Manage Logs', description: 'Manage system logs' },

  // Settings
  { resource: 'settings', action: 'read', displayName: 'View Settings', description: 'View application settings' },
  { resource: 'settings', action: 'update', displayName: 'Edit Settings', description: 'Edit application settings' },

  // Dashboard
  { resource: 'dashboard', action: 'read', displayName: 'View Dashboard', description: 'Access dashboard' },
  { resource: 'admin', action: 'access', displayName: 'Admin Access', description: 'Access admin panel' },
];

// Default system roles
export const SYSTEM_ROLES = [
  {
    name: 'super_admin',
    displayName: 'Super Administrator',
    description: 'Full system access with all permissions',
    permissions: SYSTEM_PERMISSIONS.map(p => `${p.resource}:${p.action}`)
  },
  {
    name: 'admin',
    displayName: 'Administrator',
    description: 'Administrative access with most permissions',
    permissions: [
      'users:create', 'users:read', 'users:update', 'users:delete',
      'roles:read', 'roles:assign',
      'workflows:read', 'workflows:update', 'workflows:delete',
      'marketplace:read', 'marketplace:manage',
      'analytics:read', 'logs:read',
      'settings:read', 'settings:update',
      'dashboard:read', 'admin:access'
    ]
  },
  {
    name: 'manager',
    displayName: 'Manager',
    description: 'Management access with limited permissions',
    permissions: [
      'users:read', 'users:update',
      'workflows:read', 'workflows:update',
      'marketplace:read', 'marketplace:purchase',
      'analytics:read',
      'dashboard:read'
    ]
  },
  {
    name: 'user',
    displayName: 'User',
    description: 'Standard user access',
    permissions: [
      'workflows:create', 'workflows:read', 'workflows:update', 'workflows:delete', 'workflows:execute',
      'marketplace:read', 'marketplace:purchase',
      'dashboard:read'
    ]
  },
  {
    name: 'viewer',
    displayName: 'Viewer',
    description: 'Read-only access',
    permissions: [
      'workflows:read',
      'marketplace:read',
      'dashboard:read'
    ]
  }
];

// Permission checking utilities
export function hasPermission(user: UserWithRoles, resource: string, action: string): boolean {
  // Super admin has all permissions
  if (user.isSuperAdmin) return true;

  // Admin has most permissions except super admin actions
  if (user.isAdmin && !(resource === 'users' && action === 'manage')) return true;

  // Check role-based permissions
  const userPermissions = user.userRoles.flatMap(ur => 
    ur.role.permissions.map(p => `${p.resource}:${p.action}`)
  );

  return userPermissions.includes(`${resource}:${action}`) || 
         userPermissions.includes(`${resource}:manage`);
}

export function hasAnyPermission(user: UserWithRoles, permissions: string[]): boolean {
  return permissions.some(permission => {
    const [resource, action] = permission.split(':');
    return hasPermission(user, resource, action);
  });
}

export function hasAllPermissions(user: UserWithRoles, permissions: string[]): boolean {
  return permissions.every(permission => {
    const [resource, action] = permission.split(':');
    return hasPermission(user, resource, action);
  });
}

export function canAccessPage(user: UserWithRoles, page: string): boolean {
  const pagePermissions: Record<string, string[]> = {
    '/admin': ['admin:access'],
    '/admin/users': ['users:read'],
    '/admin/roles': ['roles:read'],
    '/admin/logs': ['logs:read'],
    '/admin/analytics': ['analytics:read'],
    '/settings': ['settings:read'],
    '/workflow-manager': ['workflows:read'],
    '/workflow-canvas': ['workflows:create'],
    '/marketplace': ['marketplace:read'],
    '/developer': ['marketplace:publish'],
  };

  const requiredPermissions = pagePermissions[page];
  if (!requiredPermissions) return true; // No specific permissions required

  return hasAnyPermission(user, requiredPermissions);
}

// Activity logging helpers
export interface ActivityLog {
  action: string;
  resource?: string;
  resourceId?: string;
  details?: Record<string, any>;
}

export function createActivityLog(
  action: string,
  resource?: string,
  resourceId?: string,
  details?: Record<string, any>
): ActivityLog {
  return {
    action,
    resource,
    resourceId,
    details
  };
}

// Common activity actions
export const ACTIVITY_ACTIONS = {
  // Authentication
  LOGIN: 'login',
  LOGOUT: 'logout',
  LOGIN_FAILED: 'login_failed',
  
  // User Management
  USER_CREATED: 'user_created',
  USER_UPDATED: 'user_updated',
  USER_DELETED: 'user_deleted',
  USER_STATUS_CHANGED: 'user_status_changed',
  
  // Role Management
  ROLE_ASSIGNED: 'role_assigned',
  ROLE_REMOVED: 'role_removed',
  ROLE_CREATED: 'role_created',
  ROLE_UPDATED: 'role_updated',
  ROLE_DELETED: 'role_deleted',
  
  // Workflow Management
  WORKFLOW_CREATED: 'workflow_created',
  WORKFLOW_UPDATED: 'workflow_updated',
  WORKFLOW_DELETED: 'workflow_deleted',
  WORKFLOW_EXECUTED: 'workflow_executed',
  
  // Marketplace
  NODE_PURCHASED: 'node_purchased',
  NODE_INSTALLED: 'node_installed',
  NODE_PUBLISHED: 'node_published',
  
  // Settings
  SETTINGS_UPDATED: 'settings_updated',
  
  // General
  PAGE_ACCESSED: 'page_accessed',
  API_CALLED: 'api_called',
} as const;
