"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BookOpen,
  LifeBuoy,
  Settings2,
  Workflow,
  Store,
  Home,
  MessageSquare,
  Code
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import { SidebarThemeToggle } from "@/components/theme-toggle"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useSession } from "next-auth/react"

const getNavigationData = (user: any, pathname: string) => ({
  user: {
    name: user?.name || user?.username || "User",
    email: user?.email || "",
    avatar: user?.avatar || "",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: Home,
      isActive: pathname === "/dashboard",
    },
    {
      title: "Workflows",
      url: "/workflow-manager",
      icon: Workflow,
      isActive: pathname.startsWith("/workflow"),
      items: [
        {
          title: "All Workflows",
          url: "/workflow-manager",
          isActive: pathname === "/workflow-manager",
        },
        {
          title: "Create New",
          url: "/workflow-canvas",
          isActive: pathname.startsWith("/workflow-canvas"),
        },
      ],
    },
    {
      title: "Marketplace",
      url: "/marketplace",
      icon: Store,
      isActive: pathname.startsWith("/marketplace"),
      items: [
        {
          title: "Browse Nodes",
          url: "/marketplace",
          isActive: pathname === "/marketplace",
        },
        {
          title: "My Library",
          url: "/marketplace/library",
          isActive: pathname === "/marketplace/library",
        },
        {
          title: "Updates",
          url: "/marketplace/updates",
          isActive: pathname === "/marketplace/updates",
        },
        {
          title: "Analytics",
          url: "/marketplace/analytics",
          isActive: pathname === "/marketplace/analytics",
        },
        {
          title: "Subscription",
          url: "/marketplace/subscription",
          isActive: pathname === "/marketplace/subscription",
        },
        {
          title: "Billing",
          url: "/marketplace/billing",
          isActive: pathname === "/marketplace/billing",
        },
      ],
    },
    {
      title: "Developer",
      url: "/developer",
      icon: Code,
      isActive: pathname.startsWith("/developer"),
      items: [
        {
          title: "Dashboard",
          url: "/developer",
          isActive: pathname === "/developer",
        },
        {
          title: "Upload Node",
          url: "/developer/upload",
          isActive: pathname === "/developer/upload",
        },
        {
          title: "My Nodes",
          url: "/developer/nodes",
          isActive: pathname === "/developer/nodes",
        },
        {
          title: "Analytics",
          url: "/developer/analytics",
          isActive: pathname === "/developer/analytics",
        },
        {
          title: "Billing",
          url: "/developer/billing",
          isActive: pathname === "/developer/billing",
        },
      ],
    },
    {
      title: "Settings",
      url: "/profile",
      icon: Settings2,
      isActive: pathname.startsWith("/profile") || pathname.startsWith("/settings"),
      items: [
        {
          title: "Profile",
          url: "/profile",
          isActive: pathname === "/profile",
        },
        {
          title: "Application",
          url: "/settings",
          isActive: pathname === "/settings",
        },
        {
          title: "Preferences",
          url: "/preferences",
          isActive: pathname === "/preferences",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Documentation",
      url: "/docs",
      icon: BookOpen,
    },
    {
      title: "Support",
      url: "/support",
      icon: LifeBuoy,
    },
    {
      title: "Feedback",
      url: "/feedback",
      icon: MessageSquare,
    },
  ],
  projects: [
    {
      name: "Recent Workflows",
      url: "/workflow-manager",
      icon: Workflow,
    },
  ],
});

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const user = session?.user;
  const data = getNavigationData(user, pathname || '/');

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/dashboard">
                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Workflow className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">WorkflowAI</span>
                  <span className="truncate text-xs">Automation Platform</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <SidebarThemeToggle />
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
